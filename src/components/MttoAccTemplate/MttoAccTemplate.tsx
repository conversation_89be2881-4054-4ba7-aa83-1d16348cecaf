import { FC } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData } from "./mttoAccTemplateMockData";

type MttoAccTemplateProps = {};

const MttoAccTemplate: FC<MttoAccTemplateProps> = () => {
  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE PLANTILLAS CONTABLES</h1>
      <Table
        id="accTemplates"
        data={mockData}
        columns={mockColumns}
        filters={[
          "company",
          "area",
          "description",
          "concept",
          "movementType",
          "module",
          "templateId",
        ]}
      />
    </div>
  );
};

export default MttoAccTemplate;
